import os
import openai
import faiss
import tiktoken
from pathlib import Path
from typing import List
from xml.etree import ElementTree as ET
from openai import OpenAI
from sentence_transformers import SentenceTransformer
import numpy as np

# --- CONFIGURATION --- tester autre rate limt
openai_api_key = "********************************************************************************************************************************************************************"
assert openai_api_key.startswith("sk-"), "Clé API invalide"

client = OpenAI(api_key=openai_api_key)
model = SentenceTransformer("all-mpnet-base-v2")

# --- FONCTIONS ---

def charger_et_decouper_xml(fichier: str) -> List[str]:
    """Charge le XML et extrait les balises utiles regroupées par blocs de 10, avec priorité aux IMPLICATION"""
    with open(fichier, 'r', encoding='utf-8') as f:
        contenu = f.read()
    root = ET.fromstring(contenu)
    balises_interessantes = []

    # Extraire les balises IMPLICATION et leurs dépendances
    for tag in root.iter():
        if tag.tag == "IMPLICATION":
            # Inclure la balise IMPLICATION avec son contenu
            attributs = " ".join([f'{k}="{v}"' for k, v in tag.attrib.items()])
            contenu_balise = tag.text.strip() if tag.text else ""
            balises_interessantes.append(f'<{tag.tag} {attributs}>{contenu_balise}</{tag.tag}>')

            # Inclure les balises référencées dans IMPLICATION (e.g., PROPERTY, REFERENCE)
            for child in tag.iter():
                if child.tag in {"PROPERTY", "REFERENCE", "CONDITION", "OPTION", "WC", "CustomerPriceGroup", "PARTS", "BUTTONS"}:
                    attributs_child = " ".join([f'{k}="{v}"' for k, v in child.attrib.items()])
                    contenu_child = child.text.strip() if child.text else ""
                    balises_interessantes.append(f'<{child.tag} {attributs_child}>{contenu_child}</{child.tag}>')

    # Log des balises extraites pour débogage
    print("\n[DEBUG] Balises extraites :", len(balises_interessantes))

    # Regroupement en blocs de 10
    blocs_groupes = []
    for i in range(0, len(balises_interessantes), 10):
        bloc = "\n".join(balises_interessantes[i:i+10])
        blocs_groupes.append(bloc)

    # Regroupement en blocs de 5
    #for i in range(0, len(balises_interessantes), 5):
    #    bloc = "\n".join(balises_interessantes[i:i+5])
    #    blocs_groupes.append(bloc)

    return blocs_groupes

def indexer_blocs(blocs: List[str]):
    """Crée un index FAISS à partir des embeddings"""
    embeddings = model.encode(blocs, convert_to_numpy=True)
    index = faiss.IndexFlatL2(embeddings.shape[1])
    index.add(embeddings)
    return index, blocs, embeddings

def trouver_contextes(question: str, index, blocs: List[str], embeddings, top_k=5, seuil=1.0):
    """Trouve les blocs les plus proches de la question"""
    question_embedding = model.encode([question], convert_to_numpy=True)
    distances, indices = index.search(question_embedding, top_k)

    # Recherche par mots-clés
    mots_cles = ["WC.WC@option(\"VE\")@visible = ", "CustomerPriceGroup", "@visible = ", "@enabled = ", "FP2.FP08@enabled = "]
    contextes_par_mots_cles = [
        bloc for bloc in blocs if any(mot in bloc for mot in mots_cles)
    ]

    # Recherche par embeddings
    contextes_par_embeddings = []
    for i, dist in zip(indices[0], distances[0]):
        if dist < seuil and blocs[i] not in contextes_par_embeddings:  # Éviter les doublons
            contextes_par_embeddings.append(blocs[i])

    # Fusionner les résultats
    contextes = list(set(contextes_par_mots_cles + contextes_par_embeddings))

    # Log des distances et des indices pour le débogage
    print("\n[DEBUG] Distances des blocs pertinents :", distances[0])
    print("[DEBUG] Indices des blocs pertinents :", indices[0])

    return contextes

def interroger_gpt(question: str, contextes: List[str]) -> str:
    if not contextes:
        return "Aucun contexte XML pertinent n'a été trouvé pour répondre à cette question."

    prompt = f"""
Tu es un assistant expert en configuration de produits XML.

Voici des extraits XML contenant des règles importantes (IMPLICATION) et leurs dépendances :

{chr(10).join(contextes)}

Question : {question}

Réponds de façon claire en te basant uniquement sur le contenu XML fourni ci-dessus.
Ne fais pas d'hypothèses en dehors du XML.
"""

    response = client.chat.completions.create(
        model="gpt-4.1-mini", 
        messages=[
            {"role": "system", "content": "Tu es un expert XML pour configurateurs industriels (econn)."},
            {"role": "user", "content": prompt}
        ],
        temperature=0.2,
    )
    return response.choices[0].message.content.strip()

# --- MAIN ---
def main():
    print("Assistant IA pour interroger un fichier XML (CONFIGURATOR)")
    fichier = input("Chemin vers le fichier XML : ").strip()

    print("\n📦 Indexation du fichier...")
    blocs = charger_et_decouper_xml(fichier)
    index, blocs, embeddings = indexer_blocs(blocs)
    print(f"{len(blocs)} blocs indexés.")

    print("\n✅ Prêt ! Pose ta question sur le XML ('exit' pour quitter)")
    while True:
        question = input("\n❓ Question > ")
        if question.lower() in ["quit", "exit"]:
            break
        contextes = trouver_contextes(question, index, blocs, embeddings)
        reponse = interroger_gpt(question, contextes)
        print("\n🧠 Réponse de l'IA:\n")
        print(reponse)

if __name__ == "__main__":
    main()
