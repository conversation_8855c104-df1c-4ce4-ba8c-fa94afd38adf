#!/usr/bin/env python3
"""
Script de test pour vérifier la connexion à l'API Claude
Utilisez ce script pour tester votre clé API avant d'utiliser le système complet
"""

import anthropic
import os

def test_claude_api():
    """Test de base de l'API Claude"""
    print("🔧 Test de connexion à l'API Claude Sonnet")
    
    # Demander la clé API à l'utilisateur
    api_key = input("Entrez votre clé API Anthropic (ou 'skip' pour passer) : ").strip()
    
    if api_key.lower() == 'skip':
        print("⏭️  Test ignoré")
        return False
    
    if not api_key:
        print("❌ Clé API vide")
        return False
    
    try:
        # Initialiser le client
        client = anthropic.Anthropic(api_key=api_key)
        
        # Test simple
        print("📡 Envoi d'une requête de test...")
        response = client.messages.create(
            model="claude-3-5-sonnet-20241022",
            max_tokens=100,
            temperature=0.1,
            system="Tu es un assistant de test.",
            messages=[
                {"role": "user", "content": "Dis simplement 'Test réussi' si tu me reçois."}
            ]
        )
        
        result = response.content[0].text.strip()
        print(f"✅ Réponse de Claude : {result}")
        
        # Test de gestion des tokens
        print("\n📊 Test de gestion des tokens...")
        long_text = "A" * 1000  # Texte de 1000 caractères
        response2 = client.messages.create(
            model="claude-3-5-sonnet-20241022",
            max_tokens=50,
            temperature=0.1,
            system="Tu es un assistant de test.",
            messages=[
                {"role": "user", "content": f"Compte le nombre de caractères dans ce texte : {long_text}"}
            ]
        )
        
        result2 = response2.content[0].text.strip()
        print(f"✅ Test de tokens : {result2}")
        
        return True
        
    except anthropic.AuthenticationError:
        print("❌ Erreur d'authentification : Clé API invalide")
        return False
    except anthropic.RateLimitError:
        print("⚠️  Limite de taux atteinte : Attendez avant de réessayer")
        return False
    except anthropic.APIError as e:
        print(f"❌ Erreur API : {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue : {e}")
        return False

def test_token_estimation():
    """Test de l'estimation de tokens"""
    print("\n🧮 Test d'estimation de tokens")
    
    test_texts = [
        "Hello world",
        "Ceci est un test plus long avec plusieurs mots et caractères spéciaux !@#$%",
        "<IMPLICATION><PROPERTY name='test' value='123'/></IMPLICATION>",
        "A" * 200  # 200 caractères
    ]
    
    for i, text in enumerate(test_texts, 1):
        estimated_tokens = len(text) // 4  # Notre estimation simple
        print(f"Texte {i}: {len(text)} caractères -> ~{estimated_tokens} tokens")

def show_usage_tips():
    """Affiche des conseils d'utilisation"""
    print("\n💡 Conseils pour utiliser aiclaudeindexerV2.py :")
    print("1. Obtenez une clé API sur https://console.anthropic.com/")
    print("2. Modifiez la ligne 12 du fichier aiclaudeindexerV2.py")
    print("3. Remplacez 'your-anthropic-api-key-here' par votre vraie clé")
    print("4. Installez les dépendances : pip install anthropic")
    print("5. Lancez : python aiclaudeindexerV2.py")
    print("\n⚠️  Attention aux limites :")
    print("- 200 000 tokens maximum par requête")
    print("- Le système réduit automatiquement les contextes si nécessaire")
    print("- Surveillez les messages [DEBUG] pour voir la réduction")

if __name__ == "__main__":
    print("🚀 Test de configuration Claude pour aiclaudeindexerV2\n")
    
    success = test_claude_api()
    test_token_estimation()
    show_usage_tips()
    
    if success:
        print("\n🎉 Configuration Claude prête ! Vous pouvez utiliser aiclaudeindexerV2.py")
    else:
        print("\n🔧 Configurez votre clé API avant d'utiliser le système principal")
