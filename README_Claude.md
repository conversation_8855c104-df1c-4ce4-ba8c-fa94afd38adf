# AI Claude Indexer V2

Version Claude Sonnet du système d'indexation et d'interrogation de fichiers XML pour configurateurs industriels.

## Description

Ce script est une adaptation du `aiopenaiindexerV2.py` qui utilise **Claude 3.5 Sonnet d'Anthropic** au lieu d'OpenAI GPT pour analyser et répondre aux questions sur des fichiers XML de configuration.

## Fonctionnalités

- **Indexation intelligente** : Extrait et indexe les balises XML importantes (IMPLICATION, PROPERTY, REFERENCE, etc.)
- **Recherche hybride** : Combine recherche par embeddings (FAISS) et recherche par mots-clés
- **IA Claude Sonnet** : Utilise Claude 3.5 Sonnet pour des réponses expertes sur les configurations XML
- **Interface interactive** : Permet de poser des questions en langage naturel sur le contenu XML

## Installation

1. **Installer les dépendances** :
```bash
pip install anthropic faiss-cpu sentence-transformers tiktoken numpy
```

2. **Obtenir une clé API Anthropic** :
   - Créez un compte sur [console.anthropic.com](https://console.anthropic.com/)
   - G<PERSON><PERSON>rez une clé API
   - Remplacez `"your-anthropic-api-key-here"` dans le fichier par votre vraie clé API

## Configuration

Modifiez la ligne 12 du fichier `aiclaudeindexerV2.py` :

```python
anthropic_api_key = "votre-vraie-clé-api-anthropic"
```

## Utilisation

```bash
python aiclaudeindexerV2.py
```

Le script vous demandera :
1. Le chemin vers votre fichier XML
2. Vos questions sur le contenu XML

## Différences avec la version OpenAI

| Aspect | Version OpenAI | Version Claude |
|--------|----------------|----------------|
| **Modèle IA** | GPT-4.1-mini | Claude 3.5 Sonnet |
| **API** | OpenAI | Anthropic |
| **Fonction principale** | `interroger_gpt()` | `interroger_claude()` |
| **Gestion d'erreurs** | Basique | Améliorée avec try/catch |
| **Paramètres** | temperature=0.2 | temperature=0.2, max_tokens=1024 |

## Avantages de Claude Sonnet

- **Analyse XML experte** : Excellente compréhension des structures XML complexes
- **Réponses précises** : Moins d'hallucinations, plus fidèle au contenu fourni
- **Gestion des contextes** : Meilleure gestion des longs contextes XML
- **Sécurité** : Anthropic met l'accent sur la sécurité et l'alignement

## Structure du code

```
aiclaudeindexerV2.py
├── Configuration (API Anthropic)
├── charger_et_decouper_xml() - Extraction des balises XML
├── indexer_blocs() - Création de l'index FAISS
├── trouver_contextes() - Recherche hybride
├── interroger_claude() - Interface avec Claude Sonnet
└── main() - Boucle interactive
```

## Exemple d'utilisation

```
Assistant IA pour interroger un fichier XML (CONFIGURATOR) - Version Claude Sonnet
Chemin vers le fichier XML : config.xml

📦 Indexation du fichier...
[DEBUG] Balises extraites : 150
15 blocs indexés.

✅ Prêt ! Pose ta question sur le XML ('exit' pour quitter)

❓ Question > Quelles sont les conditions pour activer l'option VE ?

🧠 Réponse de Claude:

D'après le XML fourni, l'option VE est activée quand...
```

## Support

Pour toute question ou problème :
1. Vérifiez que votre clé API Anthropic est correcte
2. Assurez-vous que toutes les dépendances sont installées
3. Vérifiez le format de votre fichier XML

## Licence

Même licence que le projet original.
