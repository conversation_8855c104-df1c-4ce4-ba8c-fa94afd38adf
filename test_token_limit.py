#!/usr/bin/env python3
"""
Script de test pour vérifier la gestion des limites de tokens dans aiclaudeindexerV2.py
"""

from aiclaudeindexerV2 import compter_tokens_approximatif, reduire_contextes

def test_comptage_tokens():
    """Test de la fonction de comptage de tokens"""
    print("=== Test du comptage de tokens ===")
    
    texte_court = "Hello world"
    tokens_court = compter_tokens_approximatif(texte_court)
    print(f"Texte court: '{texte_court}' -> {tokens_court} tokens")
    
    texte_long = "A" * 1000  # 1000 caractères
    tokens_long = compter_tokens_approximatif(texte_long)
    print(f"Texte long: {len(texte_long)} caractères -> {tokens_long} tokens")
    
    print(f"Ratio approximatif: {len(texte_long) / tokens_long:.1f} caractères par token")

def test_reduction_contextes():
    """Test de la fonction de réduction des contextes"""
    print("\n=== Test de la réduction des contextes ===")
    
    # Créer des contextes de test de différentes tailles
    contextes_test = [
        "<IMPLICATION>Règle courte</IMPLICATION>",
        "<IMPLICATION>" + "X" * 1000 + "</IMPLICATION>",  # Contexte moyen
        "<IMPLICATION>" + "Y" * 5000 + "</IMPLICATION>",  # Contexte long
        "<IMPLICATION>Autre règle courte</IMPLICATION>",
        "<IMPLICATION>" + "Z" * 10000 + "</IMPLICATION>", # Contexte très long
    ]
    
    question_test = "Quelle est la règle pour l'option VE ?"
    
    print(f"Contextes originaux: {len(contextes_test)}")
    for i, ctx in enumerate(contextes_test):
        tokens = compter_tokens_approximatif(ctx)
        print(f"  Contexte {i+1}: {tokens} tokens")
    
    # Test avec limite normale
    print(f"\n--- Test avec limite normale (180k tokens) ---")
    contextes_reduits = reduire_contextes(contextes_test, question_test, max_tokens=180000)
    print(f"Contextes après réduction: {len(contextes_reduits)}")
    
    # Test avec limite très basse
    print(f"\n--- Test avec limite basse (1k tokens) ---")
    contextes_reduits_bas = reduire_contextes(contextes_test, question_test, max_tokens=1000)
    print(f"Contextes après réduction stricte: {len(contextes_reduits_bas)}")
    
    # Test avec limite très haute
    print(f"\n--- Test avec limite haute (500k tokens) ---")
    contextes_reduits_haut = reduire_contextes(contextes_test, question_test, max_tokens=500000)
    print(f"Contextes avec limite haute: {len(contextes_reduits_haut)}")

def test_estimation_tokens_reel():
    """Test avec un exemple plus réaliste de contenu XML"""
    print("\n=== Test avec contenu XML réaliste ===")
    
    xml_exemple = """<IMPLICATION id="rule_001">
    <CONDITION>
        <PROPERTY name="WC.WC" option="VE" visible="true"/>
        <PROPERTY name="CustomerPriceGroup" value="PREMIUM"/>
    </CONDITION>
    <REFERENCE target="FP2.FP08" enabled="true"/>
    <PARTS>
        <PART id="component_123" required="true"/>
        <PART id="component_456" optional="false"/>
    </PARTS>
    <BUTTONS>
        <BUTTON action="validate" visible="true"/>
        <BUTTON action="cancel" visible="false"/>
    </BUTTONS>
</IMPLICATION>"""
    
    tokens = compter_tokens_approximatif(xml_exemple)
    print(f"Exemple XML ({len(xml_exemple)} caractères) -> {tokens} tokens")
    
    # Simuler un grand nombre de règles similaires
    contextes_realistes = [xml_exemple.replace("rule_001", f"rule_{i:03d}") for i in range(1, 101)]
    
    total_tokens = sum(compter_tokens_approximatif(ctx) for ctx in contextes_realistes)
    print(f"100 règles similaires -> {total_tokens} tokens total")
    
    question = "Comment activer l'option VE pour les clients premium ?"
    contextes_reduits = reduire_contextes(contextes_realistes, question, max_tokens=50000)
    
    tokens_reduits = sum(compter_tokens_approximatif(ctx) for ctx in contextes_reduits)
    print(f"Après réduction: {len(contextes_reduits)} contextes, {tokens_reduits} tokens")

if __name__ == "__main__":
    print("🧪 Tests de gestion des limites de tokens pour Claude\n")
    
    test_comptage_tokens()
    test_reduction_contextes()
    test_estimation_tokens_reel()
    
    print("\n✅ Tests terminés !")
